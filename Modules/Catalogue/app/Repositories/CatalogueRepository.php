<?php

namespace Modules\Catalogue\app\Repositories;

use Modules\Catalogue\Models\Catalogue;
use App\Repositories\Admin\Repository;

class CatalogueRepository extends Repository
{
    /**
     * CatalogueRepository constructor.
     * @param Catalogue $model
     */
    public function __construct(Catalogue $model)
    {
        $this->model = $model;
    }

    /**
     * Get all catalogues with available files
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getCataloguesWithFiles()
    {
        return $this->model->all()->filter(function ($catalogue) {
            // Only return catalogues that have at least one file (English or Arabic)
            return $catalogue->hasEnglishFile() || $catalogue->hasArabicFile();
        });
    }

    /**
     * Get all active catalogues (for frontend display)
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getActiveCatalogues()
    {
        return $this->getCataloguesWithFiles();
    }

    /**
     * Get catalogue by ID with file availability check
     *
     * @param int $id
     * @return Catalogue|null
     */
    public function getCatalogueWithFiles($id)
    {
        $catalogue = $this->model->find($id);
        
        if ($catalogue && ($catalogue->hasEnglishFile() || $catalogue->hasArabicFile())) {
            return $catalogue;
        }
        
        return null;
    }

    /**
     * Handle bulk actions for catalogues
     *
     * @param array $ids
     * @param string $status
     * @param string $actionType
     * @return array
     */
    public function bulkAction($ids, $status, $actionType): array
    {
        $type = 'success';
        switch ($actionType) {
            case 'delete':
                foreach ($ids as $id) {
                    $catalogue = $this->model->findOrFail($id);
                    
                    // Delete associated files
                    if ($catalogue->file_path_en) {
                        deleteImage($catalogue->file_path_en, config('catalogue.folders.catalogue') . '/');
                    }
                    if ($catalogue->file_path_ar) {
                        deleteImage($catalogue->file_path_ar, config('catalogue.folders.catalogue') . '/');
                    }
                    
                    $catalogue->delete();
                }
                $message = __('catalogue::catalogue.catalogue_deleted_successfully');
                break;

            default:
                $type = 'error';
                $message = __('catalogue::catalogue.something_wrong');
                break;
        }
        
        return [
            'type' => $type,
            'message' => $message
        ];
    }
}
