<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\View\View;
use Modules\Author\app\Repositories\AuthorRepository;
use Modules\Book\app\Repositories\BookRepository;
use Modules\Page\app\Repositories\PageRepository;
use Modules\News\app\Repositories\NewsRepository;
use Modules\Catalogue\app\Repositories\CatalogueRepository;

class HomeController extends Controller
{
    protected $bookRepository;
    protected $pageRepository;
    protected $authorRepository;
    protected $newsRepository;
    protected $catalogueRepository;

    public function __construct(
        BookRepository $bookRepository,
        PageRepository $pageRepository,
        AuthorRepository $authorRepository,
        NewsRepository $newsRepository,
        CatalogueRepository $catalogueRepository
    ) {
        $this->bookRepository = $bookRepository;
        $this->pageRepository = $pageRepository;
        $this->authorRepository = $authorRepository;
        $this->newsRepository = $newsRepository;
        $this->catalogueRepository = $catalogueRepository;
    }

    /**
     * Display the homepage with featured content.
     */
    public function index(): View
    {
        $books = $this->bookRepository->getPublishedBooks();
        $homePage = $this->pageRepository->getPageWithActiveSectionsBySlug('home');
        $homeSections = $homePage ? $homePage->sections : collect();
        $authors = $this->authorRepository->getActiveAuthors();
        $latestNews = $this->newsRepository->getLatestNews();
        $catalogues = $this->catalogueRepository->getActiveCatalogues();

        return view('frontend.home.index', [
            'books' => $books,
            'homeSections' => $homeSections,
            'authors' => $authors,
            'latestNews' => $latestNews,
            'catalogues' => $catalogues
        ]);
    }
}
