@extends('frontend.layouts.page')
@section('title', __('navigation.home'))

@section('content')
    <!-- Hero Section -->
    <section
        class="relative hero-section flex flex-col items-center justify-center bg-repeat bg-center pt-[40px] pb-[40px] lg:pb-[0px]"
        style="background-image: url('images/background.svg');" aria-label="Introduction to The Agency hero section">

        <!-- Top gradient overlay -->
        <div class="absolute top-0 left-0 w-full h-[55%] bg-gradient-to-b from-white to-transparent z-0"></div>

        <!-- Bottom gradient overlay -->
        <div class="absolute bottom-0 left-0 w-full h-[15%] bg-gradient-to-t from-white to-transparent z-0"></div>

        <!-- Content wrapper -->
        <div class="relative z-10 text-center max-w-[684px] mx-auto px-4 sm:px-6 lg:px-8">
            <h1 class="mb-4 tracking-tight">
                Introduction to The Agency
            </h1>

            <p class="text-gray-900 text-lg leading-[1.4] mb-4">
                We offer you guidance and trustworthy allies to build a career safe from the
                lure of instant gratification and the pipe dream of easy money and fame.
            </p>
        </div>

        <!-- Book covers -->
        <div class="swiper hero-banner-swiper min-h-[600px]" tabindex="-1">
            <div class="swiper-wrapper !ease-linear">
                @foreach ($books as $book)
                    <div class="swiper-slide group focus:outline-0 focus-visible:outline-0" tabindex="-1">
                        <a href="#" tabindex="-1" class="
                            group-[.swiper-slide-active]:pt-[160px]
                            group-[.swiper-slide-active]:pb-0
                            group-[.swiper-slide-prev]:pt-[80px]
                            group-[.swiper-slide-prev]:pb-[80px]
                            group-[.swiper-slide-next]:pt-[80px]
                            group-[.swiper-slide-prev]:focus-visible:outline-0
                            group-[.swiper-slide-next]:focus-visible:outline-0
                            focus-visible:outline-0
                            focus:outline-0
                            focus:ring-0
                            pt-0
                            pb-[160px]
                            transition-all
                            duration-[3s]
                            ease-linear
                        ">
                            <article>
                                <div class="author-image mb-0">
                                    <img src="{{ $book->getCoverImageUrl('preview') }}"
                                        alt="{{ $book->title }} book cover"
                                        loading="lazy">
                                </div>
                            </article>
                        </a>
                    </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Key Services -->
    <section class="container mx-auto py-6 md:py-10 lg:pb-20 lg:pt-10" aria-label="Key Services section">
        <h2 class="mb-4 md:mb-8 xl:mb-12">Key Services</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 lg:gap-8 mx-auto">
            <article class="card-shape group flex flex-col flex-1" aria-label="Manuscript Evaluation service">
                <div class="absolute -top-[56px] -left-[1px]" aria-hidden="true">
                    <svg width="63" height="56" viewBox="0 0 63 56" stroke="#d2d2d2"
                        class="group-hover:stroke-primary-500 transition-all duration-200"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M56.5995 0C14.2807 6.99011 1.76686 39.8025 0.94043 54.995C6.21458 55.1918 19.8553 54.995 30.9072 54.995C31.2851 34.9692 52.383 25.6557 62.9088 23.5468C61.3962 17.3509 58.0166 3.96737 56.5995 0Z"
                            fill="white" />
                    </svg>
                </div>

                <div class="mb-6" aria-hidden="true">
                    <img src="{{ asset('images/manuscript_evaluation.svg') }}" alt="" class="w-16 h-16">
                </div>
                <h3 class="mb-4 leading-[1.4] text-[1.25rem] sm:text-[1.5rem]">Manuscript Evaluation</h3>
                <p class="mb-6 line-clamp-3">
                    We review your manuscript for quality, structure, and market potential. Receive
                    expert feedback to refine your work before submission or publishing.
                </p>
                <a href="#" class="btn btn-primary btn-line-effect mt-auto self-start w-auto"
                    aria-label="Learn more about Manuscript Evaluation service">
                    Learn More
                </a>
            </article>
            <article class="card-shape group flex flex-col flex-1" aria-label="Contract Negotiation service">
                <!-- Card Shape Decoration -->
                <div class="absolute -top-[56px] -left-[1px]" aria-hidden="true">
                    <svg width="63" height="56" viewBox="0 0 63 56" stroke="#d2d2d2"
                        class="group-hover:stroke-primary-500 transition-all duration-200"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M56.5995 0C14.2807 6.99011 1.76686 39.8025 0.94043 54.995C6.21458 55.1918 19.8553 54.995 30.9072 54.995C31.2851 34.9692 52.383 25.6557 62.9088 23.5468C61.3962 17.3509 58.0166 3.96737 56.5995 0Z"
                            fill="white" />
                    </svg>
                </div>

                <div class="mb-6" aria-hidden="true">
                    <img src="{{ asset('images/contract_negotiation.svg') }}" alt="" class="w-16 h-16">
                </div>
                <h3 class="mb-4 leading-[1.4] text-[1.25rem] sm:text-[1.5rem]">Contract Negotiation</h3>
                <p class="mb-6 line-clamp-3">
                    Get professional guidance to understand and negotiate your publishing contract.
                    We help protect your rights and ensure fair terms.
                </p>
                <a href="#" class="btn btn-primary btn-line-effect mt-auto self-start w-auto"
                    aria-label="Learn more about Contract Negotiation service">
                    Learn More
                </a>
            </article>
        </div>
    </section>

    <!-- Featured Authors -->
    <section class="relative bg-repeat bg-center py-6 md:pb-10 lg:pb-20 bg-primary-100"
        aria-label="Featured Authors section">
        <div class="absolute top-0 left-0 w-full h-full opacity-50"
            style="background-image: url('images/section-pattern.svg');"></div>
        <!-- Top gradient overlay -->
        <div class="absolute top-0 left-0 w-full h-[60%] bg-gradient-to-b from-white to-transparent z-0"></div>

        <!-- Bottom gradient overlay -->
        <div class="absolute bottom-0 left-0 w-full h-[45%] bg-gradient-to-t from-white to-transparent z-0"></div>

        <!-- Content wrapper -->
        <div class="relative z-10 container mx-auto">
            <h2 class="mb-4 md:mb-8 xl:mb-12">Featured Authors</h2>

            <!-- Swiper -->
            <div class="swiper authors-swiper mb-8 sm:mb-12 text-center">
                <div class="swiper-wrapper" role="list">
                    @foreach ($authors as $author)
                        <div class="swiper-slide" role="listitem">
                            <a href="{{ route('authors.show', $author->slug ?? $author->id) }}"
                                class="author-card group block transition-all duration-200 focus:outline-none focus:ring-0"
                                aria-label="View {{ $author->name }}'s profile and works">
                                <article>
                                    <div class="author-image">
                                        <img src="{{ $author->getProfileImageUrl('preview') }}"
                                            alt="Professional photo of {{ $author->name }}"
                                            loading="lazy">
                                    </div>
                                    <h5 class="group-hover:text-primary-500 group-focus:text-primary-500 transition-colors duration-200">
                                        {{ $author->name }}
                                    </h5>
                                </article>
                            </a>
                        </div>
                    @endforeach
                </div>

                <!-- Navigation buttons -->
                <div class="swiper-button-next sr-only" role="button" aria-label="Next author"></div>
                <div class="swiper-button-prev sr-only" role="button" aria-label="Previous author"></div>
            </div>

            <!-- View All Button -->
            <div class="text-center">
                <a href="{{ route('authors.index') }}" class="btn btn-primary btn-line-effect"
                    aria-label="View all featured authors">
                    View All
                </a>
            </div>
        </div>
    </section>

    <!-- Featured Books -->
    <section class="relative bg-repeat bg-center py-6 md:pb-10 lg:pb-20 bg-primary-100" aria-label="Featured Books section">
        <div class="absolute top-0 left-0 w-full h-full opacity-50"
            style="background-image: url('images/section-pattern.svg');"></div>
        <!-- Top gradient overlay -->
        <div class="absolute top-0 left-0 w-full h-[60%] bg-gradient-to-b from-white to-transparent z-0"></div>

        <!-- Bottom gradient overlay -->
        <div class="absolute bottom-0 left-0 w-full h-[50%] bg-gradient-to-t from-white to-transparent z-0"></div>

        <!-- Content wrapper -->
        <div class="relative z-10 container mx-auto">
            <h2 class="mb-4 md:mb-8 xl:mb-12">Featured Books</h2>

            <!-- Swiper -->
            <div class="swiper books-swiper mb-8 text-center">
                <div class="swiper-wrapper" role="list">
                    @foreach ($books as $book)
                        <div class="swiper-slide" role="listitem">
                            <a href="{{ route('books.show', $book->slug ?? $book->id) }}"
                                class="author-card group block transition-all duration-200 focus:outline-none focus:ring-0"
                                aria-label="View details for '{{ $book->title }}' book">
                                <article>
                                    <div class="author-image">
                                        <img src="{{ $book->getCoverImageUrl('preview') }}"
                                            alt="Book cover for '{{ $book->title }}'"
                                            loading="lazy">
                                    </div>
                                    <h5 class="group-hover:text-primary-500 group-focus:text-primary-500 transition-colors duration-200">
                                        {{ $book->title }}
                                    </h5>
                                </article>
                            </a>
                        </div>
                    @endforeach
                </div>

                <!-- Navigation buttons -->
                <div class="swiper-button-next sr-only" role="button" aria-label="Next book"></div>
                <div class="swiper-button-prev sr-only" role="button" aria-label="Previous book"></div>
            </div>

            <!-- View All Button -->
            <div class="text-center">
                <a href="{{ route('books.index') }}" class="btn btn-primary btn-line-effect"
                    aria-label="View all featured books">
                    View All
                </a>
            </div>
        </div>
    </section>

    <!-- Catalogs -->
    @if ($catalogues->count())
        <section class="container mx-auto" aria-label="Catalogs Download section">
            <h2 class="mb-4 md:mb-8 xl:mb-12">{{ __('navigation.catalogs') }}</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach ($catalogues as $catalogue)
                    @php
                        $currentLang = app()->getLocale();
                        $title = $currentLang === 'ar' ? $catalogue->title_ar : $catalogue->title_en;
                        $fileUrl = null;

                        // Determine which file to use based on current language and availability
                        if ($currentLang === 'ar' && $catalogue->hasArabicFile()) {
                            $fileUrl = $catalogue->file_url_ar;
                        } elseif ($currentLang === 'en' && $catalogue->hasEnglishFile()) {
                            $fileUrl = $catalogue->file_url_en;
                        } elseif ($catalogue->hasEnglishFile()) {
                            // Fallback to English if current language file not available
                            $fileUrl = $catalogue->file_url_en;
                        } elseif ($catalogue->hasArabicFile()) {
                            // Fallback to Arabic if English not available
                            $fileUrl = $catalogue->file_url_ar;
                        }
                    @endphp

                    @if ($fileUrl)
                        <!-- Catalog Item -->
                        <div class="flex items-center justify-between p-6 bg-white border border-gray-100 hover:border-primary-500 transition-colors duration-200">
                            <h4 class="text-lg font-medium text-gray-900">{{ $title ?: 'Catalog' }}</h4>
                            <a href="{{ $fileUrl }}" target="_blank"
                                class="w-[40px] h-[40px] rounded-full flex items-center justify-center bg-white transition-colors duration-200 group hover:bg-primary-500"
                                aria-label="{{ __('navigation.download') }} {{ $title }} PDF">
                                <span class="icon-import text-primary-500 text-[24px] group-hover:text-white" aria-hidden="true"></span>
                            </a>
                        </div>
                    @endif
                @endforeach
            </div>
        </section>
    @endif

    <!-- Latest News -->
    <section class="container mx-auto py-6 md:py-10 lg:py-20" aria-label="Latest News section">
        <h2 class="mb-4 md:mb-8 xl:mb-12">Latest News</h2>

        @if ($latestNews->count())
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 mb-12">
                <!-- Featured News (Left) -->
                @php $featured = $latestNews->first(); @endphp
                <article class="relative group" aria-label="Featured article: {{ $featured->title }}">
                    <div class="w-full max-w-none h-full lg:max-w-[634px] aspect-[1/1] relative overflow-hidden">
                        <img src="{{ $featured->getFeaturedImageUrl('main') }}"
                            alt="{{ $featured->title }}"
                            class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                            loading="lazy">
                        <div
                            class="absolute inset-0 bg-[linear-gradient(180deg,rgba(0,0,0,0)_15.05%,rgba(0,0,0,0.8)_112.51%)]">
                        </div>

                        <!-- Content overlay -->
                        <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
                            <!-- Author info -->
                            <div class="flex items-center gap-3 mb-4">
                                <img src="{{ $featured->author->getProfileImageUrl('thumbnail') }}"
                                    alt="{{ $featured->author?->name }}"
                                    class="rounded-full border-2 border-white/20 object-cover aspect-square"
                                    width="28" height="28" loading="lazy">
                                <span>{{ $featured->author?->name ?? 'Unknown Author' }}</span>
                                <time datetime="{{ $featured->created_at->toDateString() }}"
                                    class="flex items-center ms-auto">
                                    <span class="icon-calendar text-white text-[16px] me-[0.5rem]"></span>
                                    {{ $featured->created_at->format('M d, Y') }}
                                </time>
                            </div>

                            <h5 class="text-white mb-3 leading-tight">{{ $featured->title }}</h5>

                            <p class="text-white line-clamp-2 mb-4 leading-relaxed">
                                {{ Str::limit(html_entity_decode(strip_tags($featured->content)), 120) }}
                            </p>

                            <a href="{{ route('news.show', $featured->slug) }}" class="btn btn-white btn-line-effect"
                                aria-label="Read full article: {{ $featured->title }}">
                                READ MORE<span class="sr-only"> about {{ $featured->title }}</span>
                            </a>
                        </div>
                    </div>
                </article>

                <!-- Other News (Right) -->
                <div class="space-y-8">
                    @foreach ($latestNews->skip(1) as $news)
                        <article class="group" aria-label="Article: {{ $news->title }}">
                            <div class="flex gap-4 transition-transform duration-200">
                                <div class="flex-shrink-0 aspect-square w-[190px] overflow-hidden">
                                    <img src="{{ $news->getFeaturedImageUrl('medium') }}"
                                        alt="{{ $news->title }}"
                                        class="object-cover transition-transform duration-200 group-hover:scale-105"
                                        loading="lazy">
                                </div>

                                <div class="flex flex-col min-w-0">
                                    <div class="flex items-center gap-2 mb-3 flex-wrap">
                                        <div class="flex items-center gap-2">
                                            <img src="{{ $news->author->getProfileImageUrl('thumbnail') }}"
                                                alt="{{ $news->author?->name }}"
                                                class="rounded-full object-cover aspect-square" width="28"
                                                height="28" loading="lazy">
                                            <span>{{ $news->author?->name ?? 'Unknown Author' }}</span>
                                        </div>
                                        <time datetime="{{ $news->created_at->toDateString() }}"
                                            class="flex items-center ms-auto">
                                            <span class="icon-calendar text-[16px] me-[0.5rem]"></span>
                                            {{ $news->created_at->format('M d, Y') }}
                                        </time>
                                    </div>

                                    <h5 class="mb-2 line-clamp-2 transition-colors duration-200 group-hover:text-primary-500">
                                        {{ $news->title }}
                                    </h5>

                                    <p class="text-gray-900 line-clamp-2 xl:line-clamp-3 mb-3 xl:mb-5">
                                        {{ Str::limit(html_entity_decode(strip_tags($news->content)), 120) }}
                                    </p>

                                    <div class="mt-auto">
                                        <a href="{{ route('news.show', $news->slug) }}"
                                            class="btn btn-primary btn-line-effect"
                                            aria-label="Read full article: {{ $news->title }}">
                                            READ MORE<span class="sr-only"> about {{ $news->title }}</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </article>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- View All Button -->
        <div class="text-center">
            <a href="{{ route('news.index') }}" class="btn btn-primary btn-line-effect"
                aria-label="View all latest news articles">
                View All
            </a>
        </div>
    </section>
@endsection
